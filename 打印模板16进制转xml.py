# 读取文件内容
with open('1.md', 'r', encoding='utf-8') as file:
    hex_string = file.read().strip()

# 移除 '0x' 前缀
hex_string = hex_string[2:]

# 将十六进制字符串转换为字节
byte_data = bytes.fromhex(hex_string)

# 解码为 UTF-16 LE 文本
decoded_text = byte_data.decode('utf-16-le')

# 保存为 XML 文件
with open('template.xml', 'w', encoding='utf-8') as output_file:
    output_file.write(decoded_text)

print("解码完成，结果已保存为 template.xml")